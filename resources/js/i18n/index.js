// Internationalization system for the Building Committee app
class I18n {
  constructor() {
    this.currentLocale = localStorage.getItem('locale') || 'ar';
    this.translations = {};
    this.loadTranslations();
  }

  loadTranslations() {
    // Arabic translations
    this.translations.ar = {
      // Navigation & General
      app_name: 'لجنة العمارة',
      welcome: 'مرحباً بكم في لجنة العمارة',
      login: 'تسجيل الدخول',
      register: 'التسجيل',
      logout: 'تسجيل الخروج',
      dashboard: 'لوحة التحكم',
      loading: 'جاري التحميل...',
      save: 'حفظ',
      cancel: 'إلغاء',
      edit: 'تعديل',
      delete: 'حذف',
      create: 'إنشاء',
      add: 'إضافة',
      back: 'رجوع',
      next: 'التالي',
      previous: 'السابق',
      search: 'بحث',
      filter: 'تصفية',
      actions: 'الإجراءات',
      status: 'الحالة',
      date: 'التاريخ',
      amount: 'المبلغ',
      total: 'المجموع',
      name: 'الاسم',
      email: 'البريد الإلكتروني',
      password: 'كلمة المرور',
      confirm_password: 'تأكيد كلمة المرور',
      yes: 'نعم',
      no: 'لا',
      
      // Dashboard titles
      admin_dashboard: 'لوحة تحكم الإدارة',
      neighbor_dashboard: 'لوحة تحكم الجار',
      
      // Menu items
      expenses: 'المصروفات',
      incomes: 'الإيرادات',
      users: 'المستخدمين',
      buildings: 'المباني',
      my_building: 'مبناي',
      
      // User Management
      user_management: 'إدارة المستخدمين',
      create_user: 'إنشاء مستخدم جديد',
      edit_user: 'تعديل المستخدم',
      apartment_number: 'رقم الشقة',
      role: 'الدور',
      building: 'المبنى',
      admin: 'مدير',
      neighbor: 'جار',
      super_admin: 'مدير عام',
      
      // Expense Management
      expense_management: 'إدارة المصروفات',
      create_expense: 'إنشاء مصروف جديد',
      edit_expense: 'تعديل المصروف',
      expense_type: 'نوع المصروف',
      month: 'الشهر',
      year: 'السنة',
      automatic: 'تلقائي',
      manual: 'يدوي',
      notes: 'ملاحظات',
      
      // Income Management
      income_management: 'إدارة الإيرادات',
      create_income: 'إنشاء إيراد جديد',
      edit_income: 'تعديل الإيراد',
      payment_date: 'تاريخ الدفع',
      payment_method: 'طريقة الدفع',
      cash: 'نقداً',
      bank_transfer: 'تحويل بنكي',
      check: 'شيك',
      
      // Building Management
      building_management: 'إدارة المباني',
      create_building: 'إنشاء مبنى جديد',
      edit_building: 'تعديل المبنى',
      building_name: 'اسم المبنى',
      address: 'العنوان',
      city: 'المدينة',
      country: 'البلد',
      postal_code: 'الرمز البريدي',
      monthly_fee: 'الرسوم الشهرية',
      description: 'الوصف',
      
      // Status values
      pending: 'معلق',
      paid: 'مدفوع',
      received: 'مستلم',
      cancelled: 'ملغي',
      
      // Messages
      success: 'نجح',
      error: 'خطأ',
      warning: 'تحذير',
      info: 'معلومات',
      no_data: 'لا توجد بيانات',
      loading_menu: 'جاري تحميل القائمة...',
      
      // Months
      january: 'يناير',
      february: 'فبراير',
      march: 'مارس',
      april: 'أبريل',
      may: 'مايو',
      june: 'يونيو',
      july: 'يوليو',
      august: 'أغسطس',
      september: 'سبتمبر',
      october: 'أكتوبر',
      november: 'نوفمبر',
      december: 'ديسمبر',
      
      // Form validation
      required_field: 'هذا الحقل مطلوب',
      invalid_email: 'البريد الإلكتروني غير صحيح',
      password_mismatch: 'كلمات المرور غير متطابقة',
      
      // Notifications
      user_created: 'تم إنشاء المستخدم بنجاح',
      user_updated: 'تم تحديث المستخدم بنجاح',
      user_deleted: 'تم حذف المستخدم بنجاح',
      expense_created: 'تم إنشاء المصروف بنجاح',
      expense_updated: 'تم تحديث المصروف بنجاح',
      expense_deleted: 'تم حذف المصروف بنجاح',
      income_created: 'تم إنشاء الإيراد بنجاح',
      income_updated: 'تم تحديث الإيراد بنجاح',
      income_deleted: 'تم حذف الإيراد بنجاح',
      building_created: 'تم إنشاء المبنى بنجاح',
      building_updated: 'تم تحديث المبنى بنجاح',
      building_deleted: 'تم حذف المبنى بنجاح',

      // Additional translations
      outstanding_balance: 'الرصيد المستحق',
      total_expenses: 'إجمالي المصروفات',
      total_income: 'إجمالي الإيرادات',
      financial_summary: 'الملخص المالي',
      recent_expenses: 'المصروفات الأخيرة',
      recent_income: 'الإيرادات الأخيرة',
      view_all: 'عرض الكل',
      add_expense: 'إضافة مصروف',
      add_income: 'إضافة إيراد',
      add_user: 'إضافة مستخدم',
      add_building: 'إضافة مبنى',
      select: 'اختر',
      all: 'الكل',
      type: 'النوع',
      user: 'المستخدم',

      // Building Information
      building_information: 'معلومات المبنى',
      not_specified: 'غير محدد',
      monthly_expense_generation: 'إنشاء المصروفات الشهرية',
      generate_monthly_expenses_description: 'إنشاء المصروفات الشهرية لجميع الجيران في مبناك.',
      each_neighbor_charged: 'سيتم تحصيل',
      generate_monthly_expenses: 'إنشاء المصروفات الشهرية',
      generating: 'جاري الإنشاء...',
      no_building_assigned: 'لا يوجد مبنى مخصص لحسابك.',

      // Income page
      record_new_income: 'تسجيل إيراد جديد',
      apply_filters: 'تطبيق المرشحات',
      from_date: 'من تاريخ',
      to_date: 'إلى تاريخ',

      last_30_days: 'آخر 30 يوم',
      this_month: 'هذا الشهر',
      total_income: 'إجمالي الإيرادات',
      income_records: 'سجلات الإيرادات',
      method: 'الطريقة',
      payment_date: 'تاريخ الدفع',
      apartment: 'الشقة',
      neighbor: 'الجار',
      showing_results: 'عرض النتائج',
      to: 'إلى',
      of: 'من',
      results: 'نتيجة',
      next: 'التالي',
      previous: 'السابق',

      // Expense types and filters
      all_types: 'جميع الأنواع',
      all_months: 'جميع الشهور',
      building_services: 'خدمات المبنى',
      building_electricity: 'كهرباء المبنى',
      personal_electricity: 'كهرباء شخصية',
      water: 'مياه',
      other: 'أخرى',
      overdue: 'متأخر',
      auto: 'تلقائي',
      expense_records: 'سجلات المصروفات',

      // Additional messages and confirmations
      confirm_delete_income: 'هل أنت متأكد من حذف سجل الإيراد هذا؟',
      deleted: 'تم الحذف',
      delete_failed: 'فشل الحذف',
      failed_delete_income: 'فشل في حذف سجل الإيراد',
      updated: 'تم التحديث',
      update_failed: 'فشل التحديث',
      error_loading_incomes: 'خطأ في تحميل الإيرادات',

      // Neighbor dashboard
      my_financial_summary: 'ملخصي المالي',
      total_payments: 'إجمالي المدفوعات',
      my_expenses: 'مصروفاتي',
      my_income_records: 'سجلات إيراداتي',
      authentication_error: 'خطأ في المصادقة',
      user_not_found: 'المستخدم غير موجود. يرجى تسجيل الدخول مرة أخرى.',
      data_loaded: 'تم تحميل البيانات',
      no_financial_records: 'لم يتم العثور على سجلات مصروفات أو إيرادات لحسابك.',
      error_loading_dashboard: 'خطأ في تحميل بيانات لوحة التحكم',
      failed_load_financial_data: 'فشل في تحميل بياناتك المالية',

      // Admin dashboard
      total_buildings: 'إجمالي المباني',
      total_admins: 'إجمالي المديرين',
      total_neighbors: 'إجمالي الجيران',
      total_users: 'إجمالي المستخدمين',
      active_buildings: 'المباني النشطة',
      admins: 'المديرين',
      neighbors: 'الجيران',
      add_admin: 'إضافة مدير',
      neighbor_financial_summary: 'ملخص الجيران المالي',
      loading_dashboard_data: 'جاري تحميل بيانات لوحة التحكم...',
      created: 'تاريخ الإنشاء',
      confirm_delete_building: 'هل أنت متأكد من حذف المبنى "{name}"؟',
      failed_delete_building: 'فشل في حذف المبنى',
      confirm_delete_admin: 'هل أنت متأكد من حذف المدير "{name}"؟',
      failed_delete_admin: 'فشل في حذف المدير',

      // Building form and management
      loading_building: 'جاري تحميل المبنى...',
      building_not_found: 'المبنى غير موجود.',
      failed_load_building: 'فشل في تحميل المبنى',
      building_updated: 'تم تحديث المبنى بنجاح',
      building_created: 'تم إنشاء المبنى بنجاح',
      monthly_fee_description: 'سيتم تحصيل هذا المبلغ من كل جار شهرياً',
      saving: 'جاري الحفظ...',
      update_building: 'تحديث المبنى',
      create_building: 'إنشاء المبنى',
      failed_save_building: 'فشل في حفظ المبنى',

      // Building management page
      average_monthly_fee: 'متوسط الرسوم الشهرية',
      no_permission: 'ليس لديك صلاحية للوصول إلى هذه الصفحة.',
      failed_load_buildings: 'فشل في تحميل المباني',
      building_deleted: 'تم حذف المبنى بنجاح',

      // My building page
      not_specified: 'غير محدد',
      monthly_expense_generation: 'توليد المصروفات الشهرية',
      generate_monthly_expenses_description: 'انقر على الزر أدناه لتوليد المصروفات الشهرية لجميع الجيران في مبناك.',
      each_neighbor_charged: 'سيتم تحصيل',
      generating: 'جاري التوليد...',
      generate_monthly_expenses: 'توليد المصروفات الشهرية',
      no_building_assigned: 'لم يتم تعيين مبنى لحسابك.',
      failed_load_building_info: 'فشل في تحميل معلومات المبنى',
      confirm_generate_monthly_expenses: 'توليد المصروفات الشهرية (₪{fee}) لجميع الجيران في مبناك لشهر {month}/{year}؟',
      generated: 'تم التوليد',
      monthly_expenses_generated: 'تم توليد المصروفات الشهرية بنجاح',
      generation_failed: 'فشل التوليد',
      failed_generate_monthly_expenses: 'فشل في توليد المصروفات الشهرية',

      // Profile management
      my_profile: 'ملفي الشخصي',
      update_profile: 'تحديث الملف الشخصي',
      profile_information: 'معلومات الملف الشخصي',
      new_password: 'كلمة المرور الجديدة',
      confirm_new_password: 'تأكيد كلمة المرور الجديدة',
      leave_blank_to_keep_current: 'اتركه فارغاً للاحتفاظ بالحالي',
      profile_updated_successfully: 'تم تحديث الملف الشخصي بنجاح',
      failed_to_update_profile: 'فشل في تحديث الملف الشخصي',
      failed_to_load_profile: 'فشل في تحميل الملف الشخصي',
      password_confirmation_mismatch: 'تأكيد كلمة المرور غير متطابق',
      updating: 'جاري التحديث...',
    };

    // English translations
    this.translations.en = {
      // Navigation & General
      app_name: 'Building Committee',
      welcome: 'Welcome to Building Committee',
      login: 'Login',
      register: 'Register',
      logout: 'Logout',
      dashboard: 'Dashboard',
      loading: 'Loading...',
      save: 'Save',
      cancel: 'Cancel',
      edit: 'Edit',
      delete: 'Delete',
      create: 'Create',
      add: 'Add',
      back: 'Back',
      next: 'Next',
      previous: 'Previous',
      search: 'Search',
      filter: 'Filter',
      actions: 'Actions',
      status: 'Status',
      date: 'Date',
      amount: 'Amount',
      total: 'Total',
      name: 'Name',
      email: 'Email',
      password: 'Password',
      confirm_password: 'Confirm Password',
      yes: 'Yes',
      no: 'No',
      
      // Dashboard titles
      admin_dashboard: 'Admin Dashboard',
      neighbor_dashboard: 'Neighbor Dashboard',
      
      // Menu items
      expenses: 'Expenses',
      incomes: 'Incomes',
      users: 'Users',
      buildings: 'Buildings',
      my_building: 'My Building',
      
      // User Management
      user_management: 'User Management',
      create_user: 'Create New User',
      edit_user: 'Edit User',
      apartment_number: 'Apartment Number',
      role: 'Role',
      building: 'Building',
      admin: 'Admin',
      neighbor: 'Neighbor',
      super_admin: 'Super Admin',
      
      // Expense Management
      expense_management: 'Expense Management',
      create_expense: 'Create New Expense',
      edit_expense: 'Edit Expense',
      expense_type: 'Expense Type',
      month: 'Month',
      year: 'Year',
      automatic: 'Automatic',
      manual: 'Manual',
      notes: 'Notes',
      
      // Income Management
      income_management: 'Income Management',
      create_income: 'Create New Income',
      edit_income: 'Edit Income',
      payment_date: 'Payment Date',
      payment_method: 'Payment Method',
      cash: 'Cash',
      bank_transfer: 'Bank Transfer',
      check: 'Check',
      
      // Building Management
      building_management: 'Building Management',
      create_building: 'Create New Building',
      edit_building: 'Edit Building',
      building_name: 'Building Name',
      address: 'Address',
      city: 'City',
      country: 'Country',
      postal_code: 'Postal Code',
      monthly_fee: 'Monthly Fee',
      description: 'Description',
      
      // Status values
      pending: 'Pending',
      paid: 'Paid',
      received: 'Received',
      cancelled: 'Cancelled',
      
      // Messages
      success: 'Success',
      error: 'Error',
      warning: 'Warning',
      info: 'Info',
      no_data: 'No data available',
      loading_menu: 'Loading menu...',
      
      // Months
      january: 'January',
      february: 'February',
      march: 'March',
      april: 'April',
      may: 'May',
      june: 'June',
      july: 'July',
      august: 'August',
      september: 'September',
      october: 'October',
      november: 'November',
      december: 'December',
      
      // Form validation
      required_field: 'This field is required',
      invalid_email: 'Invalid email address',
      password_mismatch: 'Passwords do not match',
      
      // Notifications
      user_created: 'User created successfully',
      user_updated: 'User updated successfully',
      user_deleted: 'User deleted successfully',
      expense_created: 'Expense created successfully',
      expense_updated: 'Expense updated successfully',
      expense_deleted: 'Expense deleted successfully',
      income_created: 'Income created successfully',
      income_updated: 'Income updated successfully',
      income_deleted: 'Income deleted successfully',
      building_created: 'Building created successfully',
      building_updated: 'Building updated successfully',
      building_deleted: 'Building deleted successfully',

      // Additional translations
      outstanding_balance: 'Outstanding Balance',
      total_expenses: 'Total Expenses',
      total_income: 'Total Income',
      financial_summary: 'Financial Summary',
      recent_expenses: 'Recent Expenses',
      recent_income: 'Recent Income',
      view_all: 'View All',
      add_expense: 'Add Expense',
      add_income: 'Add Income',
      add_user: 'Add User',
      add_building: 'Add Building',
      select: 'Select',
      all: 'All',
      type: 'Type',
      user: 'User',

      // Building Information
      building_information: 'Building Information',
      not_specified: 'Not specified',
      monthly_expense_generation: 'Monthly Expense Generation',
      generate_monthly_expenses_description: 'Generate monthly expenses for all neighbors in your building.',
      each_neighbor_charged: 'Each neighbor will be charged',
      generate_monthly_expenses: 'Generate Monthly Expenses',
      generating: 'Generating...',
      no_building_assigned: 'No building assigned to your account.',

      // Income page
      record_new_income: 'Record New Income',
      apply_filters: 'Apply Filters',
      from_date: 'From Date',
      to_date: 'To Date',

      last_30_days: 'Last 30 Days',
      this_month: 'This Month',
      total_income: 'Total Income',
      income_records: 'Income Records',
      method: 'Method',
      payment_date: 'Payment Date',
      apartment: 'Apartment',
      neighbor: 'Neighbor',
      showing_results: 'Showing',
      to: 'to',
      of: 'of',
      results: 'results',
      next: 'Next',
      previous: 'Previous',

      // Expense types and filters
      all_types: 'All Types',
      all_months: 'All Months',
      building_services: 'Building Services',
      building_electricity: 'Building Electricity',
      personal_electricity: 'Personal Electricity',
      water: 'Water',
      other: 'Other',
      overdue: 'Overdue',
      auto: 'Auto',
      expense_records: 'Expense Records',

      // Additional messages and confirmations
      confirm_delete_income: 'Are you sure you want to delete this income record?',
      deleted: 'Deleted',
      delete_failed: 'Delete Failed',
      failed_delete_income: 'Failed to delete income record',
      updated: 'Updated',
      update_failed: 'Update Failed',
      error_loading_incomes: 'Error loading incomes',

      // Neighbor dashboard
      my_financial_summary: 'My Financial Summary',
      total_payments: 'Total Payments',
      my_expenses: 'My Expenses',
      my_income_records: 'My Income Records',
      authentication_error: 'Authentication Error',
      user_not_found: 'User not found. Please log in again.',
      data_loaded: 'Data Loaded',
      no_financial_records: 'No expenses or income records found for your account.',
      error_loading_dashboard: 'Error loading dashboard data',
      failed_load_financial_data: 'Failed to load your financial data',

      // Admin dashboard
      total_buildings: 'Total Buildings',
      total_admins: 'Total Admins',
      total_neighbors: 'Total Neighbors',
      total_users: 'Total Users',
      active_buildings: 'Active Buildings',
      admins: 'Admins',
      neighbors: 'Neighbors',
      add_admin: 'Add Admin',
      neighbor_financial_summary: 'Neighbor Financial Summary',
      loading_dashboard_data: 'Loading dashboard data...',
      created: 'Created',
      confirm_delete_building: 'Are you sure you want to delete building "{name}"?',
      failed_delete_building: 'Failed to delete building',
      confirm_delete_admin: 'Are you sure you want to delete admin "{name}"?',
      failed_delete_admin: 'Failed to delete admin',

      // Building form and management
      loading_building: 'Loading building...',
      building_not_found: 'Building not found.',
      failed_load_building: 'Failed to load building',
      building_updated: 'Building updated successfully',
      building_created: 'Building created successfully',
      monthly_fee_description: 'This amount will be charged to each neighbor monthly',
      saving: 'Saving...',
      update_building: 'Update Building',
      create_building: 'Create Building',
      failed_save_building: 'Failed to save building',

      // Building management page
      average_monthly_fee: 'Average Monthly Fee',
      no_permission: 'You do not have permission to access this page.',
      failed_load_buildings: 'Failed to load buildings',
      building_deleted: 'Building deleted successfully',

      // My building page
      not_specified: 'Not specified',
      monthly_expense_generation: 'Monthly Expense Generation',
      generate_monthly_expenses_description: 'Click the button below to generate monthly expenses for all neighbors in your building.',
      each_neighbor_charged: 'Each neighbor will be charged',
      generating: 'Generating...',
      generate_monthly_expenses: 'Generate Monthly Expenses',
      no_building_assigned: 'No building assigned to your account.',
      failed_load_building_info: 'Failed to load building information',
      confirm_generate_monthly_expenses: 'Generate monthly expenses (₪{fee}) for all neighbors in your building for {month}/{year}?',
      generated: 'Generated',
      monthly_expenses_generated: 'Monthly expenses generated successfully',
      generation_failed: 'Generation Failed',
      failed_generate_monthly_expenses: 'Failed to generate monthly expenses',

      // Profile management
      my_profile: 'My Profile',
      update_profile: 'Update Profile',
      profile_information: 'Profile Information',
      new_password: 'New Password',
      confirm_new_password: 'Confirm New Password',
      leave_blank_to_keep_current: 'leave blank to keep current',
      profile_updated_successfully: 'Profile updated successfully',
      failed_to_update_profile: 'Failed to update profile',
      failed_to_load_profile: 'Failed to load profile',
      password_confirmation_mismatch: 'Password confirmation does not match',
      updating: 'Updating...',
    };
  }

  t(key, params = {}) {
    const translation = this.translations[this.currentLocale]?.[key] || key;
    
    // Simple parameter replacement
    return Object.keys(params).reduce((str, param) => {
      return str.replace(`{${param}}`, params[param]);
    }, translation);
  }

  setLocale(locale) {
    this.currentLocale = locale;
    localStorage.setItem('locale', locale);

    // Update document direction and language
    document.documentElement.dir = locale === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = locale;

    // Force update of all Vue components by triggering a global reactive update
    this.triggerGlobalUpdate();

    // Trigger a custom event for components to react to language change
    window.dispatchEvent(new CustomEvent('localeChanged', { detail: { locale } }));
  }

  triggerGlobalUpdate() {
    // This will force all components to re-render by updating a reactive property
    window.dispatchEvent(new CustomEvent('forceUpdate'));
  }

  getLocale() {
    return this.currentLocale;
  }

  isRTL() {
    return this.currentLocale === 'ar';
  }
}

// Create global instance
const i18n = new I18n();

// Set initial direction
document.documentElement.dir = i18n.isRTL() ? 'rtl' : 'ltr';
document.documentElement.lang = i18n.getLocale();

export default i18n;
