<template>
    <div class="min-h-screen" :class="isGuestPage ? 'bg-gray-50' : 'bg-gray-100'">
        <nav v-if="isAuthenticated" :key="currentLocale" class="bg-white shadow-lg mb-6">
            <div class="max-w-7xl mx-auto px-4">
                <div class="flex justify-between h-16 items-center">
                    <div class="flex items-center">
                        <router-link to="/" class="text-lg lg:text-xl font-bold text-gray-800 hover:text-blue-600 transition-colors">{{ $t('app_name') }}</router-link>
                        <!-- Desktop navigation -->
                        <div v-if="isAuthenticated" class="hidden lg:flex ml-10 items-center space-x-4">
                            <router-link
                                v-if="isAdmin"
                                to="/admin"
                                class="text-gray-600 hover:text-gray-900"
                                active-class="text-indigo-600"
                            >
                                {{ $t('admin_dashboard') }}
                            </router-link>
                            <router-link
                                v-if="isNeighbor"
                                to="/neighbor"
                                class="text-gray-600 hover:text-gray-900"
                                active-class="text-indigo-600"
                            >
                                {{ $t('neighbor_dashboard') }}
                            </router-link>
                        </div>
                    </div>

                    <!-- Desktop right side -->
                    <div class="hidden lg:flex items-center" :class="$isRTL() ? 'space-x-reverse space-x-4' : 'space-x-4'">
                        <!-- Language Toggle -->
                        <language-toggle />

                        <template v-if="isAuthenticated">
                            <span class="text-gray-600" :class="$isRTL() ? 'ml-4' : 'mr-4'">{{ user?.name }}</span>
                            <button
                                @click="logout"
                                class="text-red-600 hover:text-red-800"
                            >
                                {{ $t('logout') }}
                            </button>
                        </template>
                        <template v-else>
                            <router-link
                                to="/login"
                                class="text-gray-600 hover:text-gray-900"
                                :class="$isRTL() ? 'ml-4' : 'mr-4'"
                                active-class="text-indigo-600"
                            >
                                {{ $t('login') }}
                            </router-link>
                        </template>
                    </div>

                    <!-- Mobile menu button -->
                    <div class="lg:hidden flex items-center space-x-2">
                        <language-toggle />
                        <button @click="mobileMenuOpen = !mobileMenuOpen" class="text-gray-500 hover:text-gray-700">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path v-if="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                                <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Mobile menu -->
                <div v-if="mobileMenuOpen" class="lg:hidden border-t border-gray-200 py-4">
                    <div class="space-y-2">
                        <template v-if="isAuthenticated">
                            <router-link
                                v-if="isAdmin"
                                to="/admin"
                                class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
                                active-class="text-indigo-600 bg-indigo-50"
                                @click="mobileMenuOpen = false"
                            >
                                {{ $t('admin_dashboard') }}
                            </router-link>
                            <router-link
                                v-if="isNeighbor"
                                to="/neighbor"
                                class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
                                active-class="text-indigo-600 bg-indigo-50"
                                @click="mobileMenuOpen = false"
                            >
                                {{ $t('neighbor_dashboard') }}
                            </router-link>
                            <div class="px-4 py-2 text-sm text-gray-500 border-t border-gray-200 mt-2 pt-2">
                                {{ user?.name }}
                            </div>
                            <button
                                @click="logout"
                                class="block w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 rounded"
                            >
                                {{ $t('logout') }}
                            </button>
                        </template>
                        <template v-else>
                            <router-link
                                to="/login"
                                class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
                                active-class="text-indigo-600 bg-indigo-50"
                                @click="mobileMenuOpen = false"
                            >
                                {{ $t('login') }}
                            </router-link>
                        </template>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Guest Navigation for all guest pages -->
        <nav v-if="!isAuthenticated && isGuestPage" :key="currentLocale" class="w-full bg-white shadow-lg">
            <div class="max-w-6xl mx-auto px-8 py-4">
                <div class="flex justify-between items-center">
                    <!-- Left - App Name -->
                    <router-link to="/" class="text-xl lg:text-2xl font-bold text-blue-900 hover:text-blue-700 transition-colors">
                        {{ $t('app_name') }}
                    </router-link>

                    <!-- Center - Navigation Menu (Desktop) -->
                    <div class="hidden md:flex space-x-8 text-lg font-medium text-gray-600">
                        <router-link to="/" class="hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('home') }}</router-link>
                        <router-link to="/about" class="hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('about') }}</router-link>
                        <router-link to="/services" class="hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('services') }}</router-link>
                        <router-link to="/contact" class="hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('contact') }}</router-link>
                    </div>

                    <!-- Right - Language Toggle and Login -->
                    <div class="flex items-center space-x-4" :class="$isRTL() ? 'space-x-reverse' : ''">
                        <language-toggle />
                        <router-link to="/login" class="bg-blue-900 hover:bg-blue-800 text-white px-4 py-2 rounded-lg transition-colors">
                            {{ $t('login') }}
                        </router-link>
                    </div>
                </div>

                <!-- Mobile Navigation Menu -->
                <div class="md:hidden mt-4 pt-4 border-t border-gray-200">
                    <div class="flex flex-col space-y-2">
                        <router-link to="/" class="block py-2 text-gray-600 hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('home') }}</router-link>
                        <router-link to="/about" class="block py-2 text-gray-600 hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('about') }}</router-link>
                        <router-link to="/services" class="block py-2 text-gray-600 hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('services') }}</router-link>
                        <router-link to="/contact" class="block py-2 text-gray-600 hover:text-gray-900 transition-colors" active-class="text-blue-600 font-semibold">{{ $t('contact') }}</router-link>
                        <router-link to="/login" class="block py-2 text-blue-600 hover:text-blue-800 transition-colors font-medium">{{ $t('login') }}</router-link>
                    </div>
                </div>
            </div>
        </nav>

        <main :class="isGuestPage ? '' : 'max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8'">
            <router-view />
        </main>
    </div>
</template>

<script>
import LanguageToggle from './LanguageToggle.vue';
import i18nMixin from '../mixins/i18nMixin.js';

export default {
    name: 'App',
    mixins: [i18nMixin],
    components: {
        LanguageToggle
    },
    data() {
        return {
            user: null,
            mobileMenuOpen: false,
            localeUpdateKey: 0
        };
    },
    computed: {
        isAuthenticated() {
            return !!localStorage.getItem('token') && !!this.user;
        },
        isAdmin() {
            return this.user?.role === 'admin' || this.user?.role === 'super_admin';
        },
        isNeighbor() {
            return this.user?.role === 'neighbor';
        },
        isHomePage() {
            return this.$route.name === 'Home';
        },
        isGuestPage() {
            return ['Home', 'About', 'Services', 'Contact'].includes(this.$route.name);
        },
        // Force reactivity for language changes
        currentLocale() {
            return this.$locale() + this.localeUpdateKey;
        }
    },
    watch: {
        '$route'() {
            // Update user data when route changes
            this.user = JSON.parse(localStorage.getItem('user') || 'null');
        }
    },
    created() {
        this.user = JSON.parse(localStorage.getItem('user') || 'null');
        // Listen for storage changes to update authentication state
        window.addEventListener('storage', this.handleStorageChange);
        // Also listen for custom events when localStorage is updated in the same tab
        window.addEventListener('auth-state-changed', this.handleAuthStateChange);
        // Listen for language changes
        window.addEventListener('localeChanged', this.handleLocaleChange);
        window.addEventListener('forceUpdate', this.handleForceUpdate);
    },
    mounted() {
        // Force update user data on mount
        this.user = JSON.parse(localStorage.getItem('user') || 'null');
        this.$forceUpdate();
    },
    beforeUnmount() {
        window.removeEventListener('storage', this.handleStorageChange);
        window.removeEventListener('auth-state-changed', this.handleAuthStateChange);
        window.removeEventListener('localeChanged', this.handleLocaleChange);
        window.removeEventListener('forceUpdate', this.handleForceUpdate);
    },
    methods: {
        handleStorageChange(event) {
            if (event.key === 'user' || event.key === 'token') {
                this.user = JSON.parse(localStorage.getItem('user') || 'null');
            }
        },
        handleAuthStateChange() {
            this.user = JSON.parse(localStorage.getItem('user') || 'null');
            this.$forceUpdate();
        },
        handleLocaleChange() {
            this.localeUpdateKey++;
            this.$forceUpdate();
        },
        handleForceUpdate() {
            this.localeUpdateKey++;
            this.$forceUpdate();
        },
        async logout() {
            try {
                await this.$axios.post('/logout');
            } catch (error) {
                console.error('Logout error:', error);
            } finally {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                this.user = null;
                // Dispatch custom event to notify other components
                window.dispatchEvent(new CustomEvent('auth-state-changed'));
                this.$router.push('/login');
            }
        }
    }
}
</script> 