<template>
  <div class="min-h-screen bg-gray-50 flex flex-col">
    <!-- Guest Nav Bar -->
    <nav v-if="!isAuthenticated" class="w-full bg-transparent py-8 px-8">
      <div class="max-w-6xl mx-auto">
        <!-- Desktop Navigation -->
        <div class="hidden md:flex justify-between items-center">
          <!-- Left side - empty for balance -->
          <div class="w-24"></div>

          <!-- Center - Navigation Menu -->
          <div class="flex space-x-16 text-lg font-medium text-gray-600">
            <router-link to="/" class="hover:text-gray-900 transition-colors font-semibold">{{ $t('home') }}</router-link>
            <a href="#about" class="hover:text-gray-900 transition-colors">{{ $t('about') }}</a>
            <a href="#services" class="hover:text-gray-900 transition-colors">{{ $t('services') }}</a>
            <a href="#contact" class="hover:text-gray-900 transition-colors">{{ $t('contact') }}</a>
          </div>

          <!-- Right side - Language Toggle -->
          <div class="w-24 flex justify-end">
            <language-toggle />
          </div>
        </div>

        <!-- Mobile Navigation -->
        <div class="md:hidden flex flex-col space-y-4">
          <!-- Language Toggle - Top Right on Mobile -->
          <div class="flex justify-end">
            <language-toggle />
          </div>

          <!-- Navigation Menu - Centered on Mobile -->
          <div class="flex justify-center space-x-8 text-lg font-medium text-gray-600">
            <router-link to="/" class="hover:text-gray-900 transition-colors font-semibold">{{ $t('home') }}</router-link>
            <a href="#about" class="hover:text-gray-900 transition-colors">{{ $t('about') }}</a>
            <a href="#services" class="hover:text-gray-900 transition-colors">{{ $t('services') }}</a>
            <a href="#contact" class="hover:text-gray-900 transition-colors">{{ $t('contact') }}</a>
          </div>
        </div>
      </div>
    </nav>

    <!-- Guest Landing/Marketing Section -->
    <div v-if="!isAuthenticated" class="flex-1 flex flex-col lg:flex-row items-center justify-between px-8 lg:px-20 py-16 lg:py-20 max-w-7xl mx-auto">
      <!-- Left: Text Content -->
      <div class="flex-1 flex flex-col items-start justify-center max-w-xl lg:max-w-2xl mb-12 lg:mb-0 fade-in">
        <h1 class="text-4xl lg:text-6xl font-bold text-blue-900 mb-6 leading-tight responsive-text">{{ $t('building_management') }}</h1>
        <p class="text-lg lg:text-xl text-gray-600 mb-8 max-w-lg leading-relaxed">
          {{ $t('building_management_short') }}
        </p>
        <router-link to="/register" class="bg-blue-900 hover:bg-blue-800 text-white font-semibold px-10 py-4 rounded-full shadow-lg text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover-lift">
          {{ $t('get_started') }}
        </router-link>
      </div>
      <!-- Right: Illustration -->
      <div class="flex-1 flex items-center justify-center w-full">
        <!-- Building Management Illustration -->
        <div class="relative w-full max-w-3xl h-auto">
          <!-- Background Circle -->
          <div class="absolute inset-0 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full opacity-40 transform scale-110"></div>

          <!-- Main SVG Illustration -->
          <svg viewBox="0 0 800 600" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-full h-auto relative z-10">
            <!-- Background Circle -->
            <circle cx="400" cy="300" r="280" fill="#E0E7FF" opacity="0.3"/>

            <!-- Building -->
            <rect x="320" y="180" width="160" height="240" rx="8" fill="#F59E42" stroke="#1E3A8A" stroke-width="3"/>
            <!-- Roof -->
            <polygon points="310,180 400,120 490,180" fill="#1E3A8A"/>

            <!-- Windows Row 1 -->
            <rect x="340" y="210" width="30" height="35" fill="#87CEEB" stroke="#1E3A8A" stroke-width="2"/>
            <rect x="385" y="210" width="30" height="35" fill="#87CEEB" stroke="#1E3A8A" stroke-width="2"/>
            <rect x="430" y="210" width="30" height="35" fill="#87CEEB" stroke="#1E3A8A" stroke-width="2"/>

            <!-- Windows Row 2 -->
            <rect x="340" y="260" width="30" height="35" fill="#87CEEB" stroke="#1E3A8A" stroke-width="2"/>
            <rect x="385" y="260" width="30" height="35" fill="#87CEEB" stroke="#1E3A8A" stroke-width="2"/>
            <rect x="430" y="260" width="30" height="35" fill="#87CEEB" stroke="#1E3A8A" stroke-width="2"/>

            <!-- Windows Row 3 -->
            <rect x="340" y="310" width="30" height="35" fill="#87CEEB" stroke="#1E3A8A" stroke-width="2"/>
            <rect x="385" y="310" width="30" height="35" fill="#87CEEB" stroke="#1E3A8A" stroke-width="2"/>
            <rect x="430" y="310" width="30" height="35" fill="#87CEEB" stroke="#1E3A8A" stroke-width="2"/>

            <!-- Windows Row 4 -->
            <rect x="340" y="360" width="30" height="35" fill="#87CEEB" stroke="#1E3A8A" stroke-width="2"/>
            <rect x="430" y="360" width="30" height="35" fill="#87CEEB" stroke="#1E3A8A" stroke-width="2"/>

            <!-- Door -->
            <rect x="385" y="360" width="30" height="60" fill="#8B4513" stroke="#1E3A8A" stroke-width="2"/>
            <circle cx="395" cy="390" r="3" fill="#FFD700"/>

            <!-- Left Person with Dollar Sign -->
            <g transform="translate(180, 380)">
              <!-- Person Body -->
              <circle cx="0" cy="0" r="20" fill="#87CEEB"/>
              <rect x="-12" y="20" width="24" height="40" fill="#6B7280" rx="4"/>
              <rect x="-15" y="25" width="30" height="20" fill="#87CEEB" rx="3"/>
              <!-- Dollar Sign -->
              <circle cx="-50" cy="10" r="25" fill="#32CD32" stroke="#1E3A8A" stroke-width="2"/>
              <text x="-50" y="18" text-anchor="middle" fill="#fff" font-size="24" font-weight="bold">$</text>
            </g>

            <!-- Right Person with Euro Sign -->
            <g transform="translate(620, 380)">
              <!-- Person Body -->
              <circle cx="0" cy="0" r="20" fill="#87CEEB"/>
              <rect x="-12" y="20" width="24" height="40" fill="#1E3A8A" rx="4"/>
              <rect x="-15" y="25" width="30" height="20" fill="#87CEEB" rx="3"/>
              <!-- Euro Sign -->
              <circle cx="50" cy="10" r="25" fill="#F59E42" stroke="#1E3A8A" stroke-width="2"/>
              <text x="50" y="18" text-anchor="middle" fill="#fff" font-size="24" font-weight="bold">€</text>
            </g>

            <!-- Info Card -->
            <rect x="480" y="240" width="140" height="100" rx="12" fill="#fff" stroke="#1E3A8A" stroke-width="2" filter="drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))"/>
            <text x="550" y="265" text-anchor="middle" fill="#1E3A8A" font-size="16" font-weight="bold">Neighbors</text>
            <text x="550" y="285" text-anchor="middle" fill="#32CD32" font-size="14" font-weight="600">Income</text>
            <text x="550" y="305" text-anchor="middle" fill="#F59E42" font-size="14" font-weight="600">Expenses</text>

            <!-- Progress Bars -->
            <rect x="500" y="290" width="100" height="6" fill="#E5E7EB" rx="3"/>
            <rect x="500" y="290" width="70" height="6" fill="#32CD32" rx="3"/>

            <rect x="500" y="310" width="100" height="6" fill="#E5E7EB" rx="3"/>
            <rect x="500" y="310" width="50" height="6" fill="#F59E42" rx="3"/>

            <!-- Money Bill -->
            <rect x="580" y="140" width="70" height="45" rx="6" fill="#fff" stroke="#9CA3AF" stroke-width="2" filter="drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1))"/>
            <text x="615" y="155" text-anchor="middle" fill="#9CA3AF" font-size="12">- $ -</text>
            <line x1="590" y1="165" x2="640" y2="165" stroke="#9CA3AF" stroke-width="1"/>
            <line x1="590" y1="170" x2="640" y2="170" stroke="#9CA3AF" stroke-width="1"/>
            <line x1="590" y1="175" x2="630" y2="175" stroke="#9CA3AF" stroke-width="1"/>

            <!-- Decorative Plants -->
            <ellipse cx="120" cy="500" rx="35" ry="18" fill="#22C55E"/>
            <ellipse cx="135" cy="490" rx="25" ry="12" fill="#16A34A"/>
            <ellipse cx="105" cy="490" rx="20" ry="10" fill="#16A34A"/>

            <ellipse cx="680" cy="500" rx="35" ry="18" fill="#22C55E"/>
            <ellipse cx="695" cy="490" rx="25" ry="12" fill="#16A34A"/>
            <ellipse cx="665" cy="490" rx="20" ry="10" fill="#16A34A"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- Authenticated User Section: Buildings List -->
    <div v-else class="max-w-5xl mx-auto px-4 py-10">
      <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">{{ $t('buildings') }}</h1>
      <div v-if="loading" class="text-center py-10">
        <span class="text-lg text-gray-500">{{ $t('loading') }}</span>
      </div>
      <div v-else-if="error" class="text-center py-10">
        <span class="text-lg text-red-500">{{ error }}</span>
      </div>
      <div v-else>
        <div v-if="buildings.length === 0" class="text-center text-gray-500 py-10">
          {{ $t('no_buildings_found') }}
        </div>
        <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div v-for="building in buildings" :key="building.id" class="bg-white rounded-lg shadow p-6 flex flex-col">
            <h2 class="text-xl font-semibold text-gray-900 mb-2">{{ building.name }}</h2>
            <div class="mb-2">
              <span class="font-medium text-gray-600">{{ $t('address') }}:</span>
              <span class="text-gray-800">{{ building.address || $t('not_specified') }}</span>
            </div>
            <div class="mb-2">
              <span class="font-medium text-gray-600">{{ $t('city') }}:</span>
              <span class="text-gray-800">{{ building.city || $t('not_specified') }}</span>
            </div>
            <div class="mb-2">
              <span class="font-medium text-gray-600">{{ $t('country') }}:</span>
              <span class="text-gray-800">{{ building.country || $t('not_specified') }}</span>
            </div>
            <div class="mb-2">
              <span class="font-medium text-gray-600">{{ $t('postal_code') }}:</span>
              <span class="text-gray-800">{{ building.postal_code || $t('not_specified') }}</span>
            </div>
            <div class="mb-2">
              <span class="font-medium text-gray-600">{{ $t('monthly_fee') }}:</span>
              <span class="text-gray-800">₪{{ parseFloat(building.monthly_fee).toFixed(2) }}</span>
            </div>
            <div v-if="building.description" class="mb-2">
              <span class="font-medium text-gray-600">{{ $t('description') }}:</span>
              <span class="text-gray-800">{{ building.description }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';
import LanguageToggle from '../components/LanguageToggle.vue';

export default {
  name: 'Home',
  mixins: [i18nMixin],
  components: {
    LanguageToggle
  },
  data() {
    return {
      buildings: [],
      loading: true,
      error: ''
    };
  },
  computed: {
    isAuthenticated() {
      const token = localStorage.getItem('token');
      const user = localStorage.getItem('user');
      return !!token && !!user;
    }
  },
  async mounted() {
    if (this.isAuthenticated) {
      try {
        const response = await this.$axios.get('/buildings');
        this.buildings = response.data;
      } catch (err) {
        this.error = this.$t('failed_load_buildings');
      } finally {
        this.loading = false;
      }
    } else {
      this.loading = false;
    }
  }
};
</script>

<style scoped>
/* Custom animations for the home page */
.fade-in {
  animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth hover effects */
.hover-lift:hover {
  transform: translateY(-2px);
  transition: transform 0.3s ease;
}

/* Responsive text sizing */
@media (max-width: 768px) {
  .responsive-text {
    font-size: 2.5rem;
    line-height: 1.2;
  }
}

@media (min-width: 1024px) {
  .responsive-text {
    font-size: 4rem;
    line-height: 1.1;
  }
}
</style>