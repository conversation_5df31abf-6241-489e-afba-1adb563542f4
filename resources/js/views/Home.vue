<template>
  <div class="min-h-screen bg-gray-50 flex flex-col">
    <!-- Guest Nav Bar -->
    <nav v-if="!isAuthenticated" class="w-full bg-transparent py-6 px-8 flex justify-end space-x-10 text-lg font-medium text-blue-900">
      <router-link to="/" class="hover:underline">Home</router-link>
      <a href="#about" class="hover:underline">About</a>
      <a href="#services" class="hover:underline">Services</a>
      <a href="#contact" class="hover:underline">Contact</a>
    </nav>

    <!-- Guest Landing/Marketing Section -->
    <div v-if="!isAuthenticated" class="flex-1 flex flex-col md:flex-row items-center justify-center px-6 md:px-16 py-10 md:py-0">
      <!-- Left: Text Content -->
      <div class="flex-1 flex flex-col items-start justify-center max-w-xl">
        <h1 class="text-5xl md:text-6xl font-extrabold text-blue-900 mb-6 leading-tight">Building<br />Management</h1>
        <p class="text-lg text-gray-700 mb-8 max-w-md">
          Lajnet Amara is your all-in-one platform for managing residential buildings, tracking expenses, handling payments, and fostering a transparent community. Simplify your building's finances and communication today.
        </p>
        <router-link to="/register" class="bg-blue-900 hover:bg-blue-800 text-white font-semibold px-8 py-4 rounded-full shadow text-lg transition mb-8">Get Started</router-link>
      </div>
      <!-- Right: Illustration Placeholder -->
      <div class="flex-1 flex items-center justify-center w-full mt-10 md:mt-0">
        <!-- SVG Illustration (simple, matching the theme) -->
        <svg viewBox="0 0 480 360" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-full max-w-lg h-auto">
          <rect x="180" y="80" width="160" height="200" rx="8" fill="#F59E42" stroke="#1E3A8A" stroke-width="4"/>
          <rect x="200" y="100" width="30" height="40" fill="#fff" stroke="#1E3A8A" stroke-width="2"/>
          <rect x="250" y="100" width="30" height="40" fill="#fff" stroke="#1E3A8A" stroke-width="2"/>
          <rect x="300" y="100" width="30" height="40" fill="#fff" stroke="#1E3A8A" stroke-width="2"/>
          <rect x="200" y="160" width="30" height="40" fill="#fff" stroke="#1E3A8A" stroke-width="2"/>
          <rect x="250" y="160" width="30" height="40" fill="#fff" stroke="#1E3A8A" stroke-width="2"/>
          <rect x="300" y="160" width="30" height="40" fill="#fff" stroke="#1E3A8A" stroke-width="2"/>
          <rect x="200" y="220" width="30" height="40" fill="#fff" stroke="#1E3A8A" stroke-width="2"/>
          <rect x="250" y="220" width="30" height="40" fill="#fff" stroke="#1E3A8A" stroke-width="2"/>
          <rect x="300" y="220" width="30" height="40" fill="#fff" stroke="#1E3A8A" stroke-width="2"/>
          <rect x="255" y="260" width="30" height="40" fill="#1E3A8A" stroke="#1E3A8A" stroke-width="2"/>
          <!-- Left Person -->
          <circle cx="120" cy="220" r="24" fill="#1E3A8A"/>
          <rect x="110" y="244" width="20" height="50" rx="8" fill="#6B7280"/>
          <ellipse cx="120" cy="300" rx="18" ry="8" fill="#D1D5DB"/>
          <circle cx="120" cy="220" r="12" fill="#fff"/>
          <rect x="90" y="270" width="60" height="30" rx="6" fill="#fff" stroke="#1E3A8A" stroke-width="2"/>
          <text x="120" y="290" text-anchor="middle" fill="#1E3A8A" font-size="16" font-weight="bold">$</text>
          <!-- Right Person -->
          <circle cx="400" cy="220" r="24" fill="#1E3A8A"/>
          <rect x="390" y="244" width="20" height="50" rx="8" fill="#6B7280"/>
          <ellipse cx="400" cy="300" rx="18" ry="8" fill="#D1D5DB"/>
          <circle cx="400" cy="220" r="12" fill="#fff"/>
          <rect x="370" y="270" width="60" height="30" rx="6" fill="#fff" stroke="#F59E42" stroke-width="2"/>
          <text x="400" y="290" text-anchor="middle" fill="#F59E42" font-size="16" font-weight="bold">€</text>
          <!-- Info Card -->
          <rect x="140" y="60" width="120" height="60" rx="8" fill="#fff" stroke="#1E3A8A" stroke-width="2"/>
          <text x="200" y="85" text-anchor="middle" fill="#1E3A8A" font-size="16" font-weight="bold">Neighbors</text>
          <text x="200" y="105" text-anchor="middle" fill="#1E3A8A" font-size="14">Income</text>
          <text x="200" y="120" text-anchor="middle" fill="#F59E42" font-size="14">Expenses</text>
        </svg>
      </div>
    </div>

    <!-- Authenticated User Section: Buildings List -->
    <div v-else class="max-w-5xl mx-auto px-4 py-10">
      <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">{{ $t('buildings') }}</h1>
      <div v-if="loading" class="text-center py-10">
        <span class="text-lg text-gray-500">{{ $t('loading') }}</span>
      </div>
      <div v-else-if="error" class="text-center py-10">
        <span class="text-lg text-red-500">{{ error }}</span>
      </div>
      <div v-else>
        <div v-if="buildings.length === 0" class="text-center text-gray-500 py-10">
          {{ $t('no_buildings_found') }}
        </div>
        <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div v-for="building in buildings" :key="building.id" class="bg-white rounded-lg shadow p-6 flex flex-col">
            <h2 class="text-xl font-semibold text-gray-900 mb-2">{{ building.name }}</h2>
            <div class="mb-2">
              <span class="font-medium text-gray-600">{{ $t('address') }}:</span>
              <span class="text-gray-800">{{ building.address || $t('not_specified') }}</span>
            </div>
            <div class="mb-2">
              <span class="font-medium text-gray-600">{{ $t('city') }}:</span>
              <span class="text-gray-800">{{ building.city || $t('not_specified') }}</span>
            </div>
            <div class="mb-2">
              <span class="font-medium text-gray-600">{{ $t('country') }}:</span>
              <span class="text-gray-800">{{ building.country || $t('not_specified') }}</span>
            </div>
            <div class="mb-2">
              <span class="font-medium text-gray-600">{{ $t('postal_code') }}:</span>
              <span class="text-gray-800">{{ building.postal_code || $t('not_specified') }}</span>
            </div>
            <div class="mb-2">
              <span class="font-medium text-gray-600">{{ $t('monthly_fee') }}:</span>
              <span class="text-gray-800">₪{{ parseFloat(building.monthly_fee).toFixed(2) }}</span>
            </div>
            <div v-if="building.description" class="mb-2">
              <span class="font-medium text-gray-600">{{ $t('description') }}:</span>
              <span class="text-gray-800">{{ building.description }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  name: 'Home',
  mixins: [i18nMixin],
  data() {
    return {
      buildings: [],
      loading: true,
      error: ''
    };
  },
  computed: {
    isAuthenticated() {
      const token = localStorage.getItem('token');
      const user = localStorage.getItem('user');
      return !!token && !!user;
    }
  },
  async mounted() {
    if (this.isAuthenticated) {
      try {
        const response = await this.$axios.get('/buildings');
        this.buildings = response.data;
      } catch (err) {
        this.error = this.$t('failed_load_buildings');
      } finally {
        this.loading = false;
      }
    } else {
      this.loading = false;
    }
  }
};
</script>

<style scoped>
</style> 